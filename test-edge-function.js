// This is a comprehensive test script to check if the Supabase Edge Function is properly configured
// Run this with Node.js: node test-edge-function.js

async function testEdgeFunction() {
  const SUPABASE_URL = "https://hhxwnoreclckmtenugmt.supabase.co";
  const SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTA2ODksImV4cCI6MjA2NTQ4NjY4OX0.Sx0m7H7YgSbOANJQ2YhlrZqsNms9J757ttQlQnOr-ek";
  const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTkxMDY4OSwiZXhwIjoyMDY1NDg2Njg5fQ.qfKkAhXg4fTVqX2gULF-aQw6EuPazZNI0EGsfYldsk0";

  // Use the service role key for testing
  const API_KEY = SUPABASE_SERVICE_ROLE_KEY;

  try {
    console.log('\n1. Testing Edge Function connectivity...');
    
    // First, test basic connectivity with OPTIONS request (CORS preflight)
    const pingResponse = await fetch(`${SUPABASE_URL}/functions/v1/manage-users`, {
      method: 'OPTIONS',
      headers: {
        'Authorization': `Bearer ${API_KEY}`
      }
    });

    console.log('CORS preflight status:', pingResponse.status);
    
    if (pingResponse.status !== 200 && pingResponse.status !== 204) {
      console.error('❌ Edge Function is not accessible. CORS preflight failed.');
      return;
    } else {
      console.log('✅ CORS preflight successful');
    }
    
    // Test the test endpoint
    console.log('\n2. Testing basic function operation...');
    const testResponse = await fetch(`${SUPABASE_URL}/functions/v1/manage-users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        action: 'test'
      })
    });

    const testData = await testResponse.json();
    console.log('Test response status:', testResponse.status);
    console.log('Test response data:', testData);

    if (!testResponse.ok) {
      console.error('❌ Basic function test failed:', testData.error);
      if (testData.stack) {
        console.error('Error stack:', testData.stack);
      }
      return;
    } else {
      console.log('✅ Basic function test successful');
    }
    
    // Now test creating a user
    console.log('\n3. Testing user creation...');
    const testUser = {
      username: 'testuser' + Math.floor(Math.random() * 10000),
      full_name: 'Test User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
      active: true
    };
    
    console.log('Attempting to create user:', testUser.username);
    
    const createResponse = await fetch(`${SUPABASE_URL}/functions/v1/manage-users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        action: 'create',
        userData: testUser
      })
    });

    const createData = await createResponse.json();
    console.log('Create user response status:', createResponse.status);
    console.log('Create user response data:', createData);

    if (createResponse.ok) {
      console.log('✅ User creation successful!');
      console.log('\n🎉 All tests passed! The Edge Function is working correctly.');
    } else {
      console.error('❌ User creation failed:', createData.error);
      if (createData.stack) {
        console.error('Error stack:', createData.stack);
      }
      console.log('\n⚠️ Some tests failed. Please check the error messages above.');
    }
  } catch (error) {
    console.error('❌ Failed to access Edge Function:', error);
    console.log('\n⚠️ Tests failed due to network or other errors.');
  }
}

testEdgeFunction();