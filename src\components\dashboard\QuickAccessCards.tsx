import { useState } from "react";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { FactoryOutputCard } from "./cards/FactoryOutputCard";
import { SettingTeamsCard } from "./cards/SettingTeamsCard";
import { DehackingCard } from "./cards/DehackingCard";
import { FuelManagementCard } from "./cards/FuelManagementCard";
import { ProductionEntryDialog } from "./dialogs/ProductionEntryDialog";
import { SettingProductionEntryDialog } from "./dialogs/SettingProductionEntryDialog";
import { FuelActionChoiceDialog } from "./dialogs/FuelActionChoiceDialog";
import { RecordFuelDispensingDialog } from "./dialogs/RecordFuelDispensingDialog";
import { RecordFuelDeliveryDialog } from "./dialogs/RecordFuelDeliveryDialog";
import { DispenseFuelDialog } from "./dialogs/DispenseFuelDialog";
import { useFuelBunkers } from "@/hooks/useFuelBunkers";

// Mock data for dialogs (REMOVE mockBunkers)
const mockAssets = [
  { id: "asset1", name: "Kiln A" },
  { id: "asset2", name: "Kiln B" },
  { id: "asset3", name: "Forklift FL001" },
  { id: "asset4", name: "Generator G002" },
  { id: "asset5", name: "Truck T003" },
];

export const QuickAccessCards = () => {
  const queryClient = useQueryClient();
  const [isProductionDialogOpen, setIsProductionDialogOpen] = useState(false);
  const [isSettingProductionDialogOpen, setIsSettingProductionDialogOpen] = useState(false);
  
  const [isFuelActionChoiceOpen, setIsFuelActionChoiceOpen] = useState(false);
  const [isRecordDispensingOpen, setIsRecordDispensingOpen] = useState(false);
  const [isRecordDeliveryOpen, setIsRecordDeliveryOpen] = useState(false);
  
  // --- New: fetch the bunkers from the database
  const { data: dbBunkers = [] } = useFuelBunkers();

  const handleSuccess = (queryKeys: string[]) => {
    queryKeys.forEach(key => {
      queryClient.invalidateQueries({ queryKey: [key] });
    });
  };

  const handleRecordProductionClick = () => {
    setIsProductionDialogOpen(true);
  };

  const handleRecordSettingProductionClick = () => {
    setIsSettingProductionDialogOpen(true);
  };

  const handleManageFuel = () => {
    toast.dismiss(); // Dismiss any existing toasts
    setIsFuelActionChoiceOpen(true);
  };

  const openRecordDispensingDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDispensingOpen(true);
  };

  const openRecordDeliveryDialog = () => {
    setIsFuelActionChoiceOpen(false);
    setIsRecordDeliveryOpen(true);
  };

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <FactoryOutputCard onRecordProduction={handleRecordProductionClick} />
        <SettingTeamsCard onRecordProduction={handleRecordSettingProductionClick} />
        <DehackingCard />
        <FuelManagementCard onManageFuel={handleManageFuel} />
      </div>

      <ProductionEntryDialog 
        isOpen={isProductionDialogOpen}
        onClose={() => setIsProductionDialogOpen(false)}
        onSuccess={() => handleSuccess(['setting_production_entries', 'settingTeamsCardSummary', 'settingSummary'])}
      />

      <SettingProductionEntryDialog
        isOpen={isSettingProductionDialogOpen}
        onClose={() => setIsSettingProductionDialogOpen(false)}
        onSuccess={() => handleSuccess(['settingTeamsCardSummary'])}
      />

      <FuelActionChoiceDialog
        isOpen={isFuelActionChoiceOpen}
        onClose={() => setIsFuelActionChoiceOpen(false)}
        onRecordDispensing={openRecordDispensingDialog}
        onRecordDelivery={openRecordDeliveryDialog}
      />

      <DispenseFuelDialog
        isOpen={isRecordDispensingOpen}
        onClose={() => setIsRecordDispensingOpen(false)}
      />

      <RecordFuelDeliveryDialog
        isOpen={isRecordDeliveryOpen}
        onClose={() => setIsRecordDeliveryOpen(false)}
        bunkers={dbBunkers.map(b => ({
          id: b.id,
          name: b.name,
        }))}
      />
    </>
  );
};
