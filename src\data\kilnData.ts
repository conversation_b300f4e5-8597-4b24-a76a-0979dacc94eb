
// Import Supabase type definitions
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Use database enums for status fields
type KilnStatus = Database["public"]["Enums"]["kiln_status"];
type FireStatus = Database["public"]["Enums"]["fire_status"];

// Fix type definitions for matching DB exactly
export interface FireConfig {
  id: string;
  name: string;
  kiln_id: string;
  status: FireStatus;
}
export interface KilnConfig {
  id: string;
  name: string;
  status: KilnStatus;
  fires: FireConfig[];
}

export const getKilns = async (): Promise<KilnConfig[]> => {
  const { data: kilnsData, error: kilnError } = await supabase
    .from('kilns')
    .select('id, name, status');

  if (kilnError) {
    console.error("Error fetching kilns:", kilnError);
    throw new Error(kilnError.message);
  }

  const { data: firesData, error: firesError } = await supabase
    .from('fires')
    .select('id, name, status, kiln_id');

  if (firesError) {
    console.error("Error fetching fires:", firesError);
    throw new Error(firesError.message);
  }

  if (!kilnsData) {
    return [];
  }
  
  const kilns: KilnConfig[] = kilnsData.map((kiln) => ({
    ...kiln,
    fires: (firesData || []).filter((fire) => fire.kiln_id === kiln.id),
  }));

  console.log("[DEBUG] Kilns loaded from Supabase:", kilns);
  return kilns;
};

export const updateKilnStatus = async ({
  id,
  status,
}: {
  id: string;
  status: KilnStatus;
}) => {
  const { data, error } = await supabase
    .from("kilns")
    .update({ status })
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error("Error updating kiln status:", error);
    throw new Error(error.message);
  }

  return data;
};

export const getFires = async () => {
  // Just fetch id, name, status, kiln_id from the fires table
  const { data, error } = await supabase
    .from("fires")
    .select("id, name, status, kiln_id");

  if (error) {
    console.error("Error fetching fires:", error);
    throw new Error(error.message);
  }

  return (data ?? []).map((fire: any) => ({
    id: fire.id,
    name: fire.name,
    status: fire.status,
    kilnId: fire.kiln_id,
  }));
};
