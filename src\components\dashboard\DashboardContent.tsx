
import { QuickAccessCards } from "./QuickAccessCards";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { KeyMetrics } from "./KeyMetrics";
import { AnalyticsCharts } from "./AnalyticsCharts";
import { FuelBunkersDashboard } from "./FuelBunkersDashboard";
import { ProductionLoss } from "./ProductionLoss";
import { useState } from "react";
import { SettingSummaryCard } from "./cards/SettingSummaryCard";
import { DehackingSummaryCard } from "./cards/DehackingSummaryCard";

export type TimeRange = "today" | "week" | "month" | "year";

export const DashboardContent = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>("week");

  return (
    <div className="space-y-6">
      <QuickAccessCards />
      
      <TimeRangeSelector 
        selectedRange={selectedTimeRange}
        onRangeChange={setSelectedTimeRange}
      />
      
      <KeyMetrics timeRange={selectedTimeRange} />
      
      <AnalyticsCharts timeRange={selectedTimeRange} />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <FuelBunkersDashboard />
        <ProductionLoss />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SettingSummaryCard timeRange={selectedTimeRange} />
        <DehackingSummaryCard timeRange={selectedTimeRange} />
      </div>
    </div>
  );
};
