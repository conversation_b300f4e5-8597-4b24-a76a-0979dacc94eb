import React, { createContext, useContext, useState, useEffect } from 'react';
import type { User } from '@/hooks/useUsers';

interface LoginCredentials {
  username: string;
  password: string;
}

interface AuthContextType {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => void;
  loginTime: Date | null;
  getSessionDuration: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users for demonstration - in real app, this would come from database
const mockUsers: User[] = [
  {
    id: "supervisor-1",
    username: "john_supervisor",
    full_name: "<PERSON>",
    email: "<EMAIL>",
    role: "factory_supervisor",
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "supervisor-2", 
    username: "mary_supervisor",
    full_name: "<PERSON>",
    email: "<EMAIL>",
    role: "yard_supervisor",
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  },
  {
    id: "admin-1",
    username: "admin",
    full_name: "System Administrator",
    email: "<EMAIL>",
    role: "admin",
    active: true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
];

// Mock password validation - in real app, this would be secure
const validateCredentials = (username: string, password: string): User | null => {
  const user = mockUsers.find(u => u.username === username);
  if (user && password === 'password123') { // Simple demo password
    return user;
  }
  return null;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loginTime, setLoginTime] = useState<Date | null>(null);

  // Check for existing session on app load
  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser');
    const savedLoginTime = localStorage.getItem('loginTime');
    
    if (savedUser && savedLoginTime) {
      try {
        setCurrentUser(JSON.parse(savedUser));
        setLoginTime(new Date(savedLoginTime));
      } catch (error) {
        console.error('Error loading saved session:', error);
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    setIsLoading(true);
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const user = validateCredentials(credentials.username, credentials.password);
    
    if (user) {
      const now = new Date();
      setCurrentUser(user);
      setLoginTime(now);
      
      // Save to localStorage for persistence
      localStorage.setItem('currentUser', JSON.stringify(user));
      localStorage.setItem('loginTime', now.toISOString());
      
      setIsLoading(false);
      return true;
    }
    
    setIsLoading(false);
    return false;
  };

  const logout = () => {
    setCurrentUser(null);
    setLoginTime(null);
    localStorage.removeItem('currentUser');
    localStorage.removeItem('loginTime');
  };

  const getSessionDuration = (): string => {
    if (!loginTime) return '0h 0m';
    
    const now = new Date();
    const diffMs = now.getTime() - loginTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  const contextValue: AuthContextType = {
    currentUser,
    isAuthenticated: !!currentUser,
    isLoading,
    login,
    logout,
    loginTime,
    getSessionDuration,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
