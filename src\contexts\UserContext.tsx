import React, { createContext, useContext, useState, useEffect } from 'react';
import type { UserRole } from '@/hooks/useUsers';

interface User {
  id: string;
  username: string;
  full_name: string;
  email?: string;
  role: UserRole;
  active: boolean;
}

interface UserContextType {
  currentUser: User | null;
  setCurrentUser: (user: User | null) => void;
  userRole: UserRole | null;
  hasAccess: (requiredRoles: UserRole[]) => boolean;
  canAccessMenuItem: (menuItem: string) => boolean;
  canAccessDashboardCard: (cardType: string) => boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

// Mock current user - in a real app, this would come from authentication
const mockCurrentUser: User = {
  id: "current-user-id",
  username: "admin",
  full_name: "System Administrator",
  email: "<EMAIL>",
  role: "admin" as UserRole,
  active: true
};

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(mockCurrentUser);

  const userRole = currentUser?.role || null;

  const hasAccess = (requiredRoles: UserRole[]): boolean => {
    if (!userRole) return false;
    return requiredRoles.includes(userRole);
  };

  const canAccessMenuItem = (menuItem: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return ['dashboard', 'reports', 'payments'].includes(menuItem);

      case 'factory_supervisor':
        return ['dashboard', 'team-management'].includes(menuItem);

      case 'yard_supervisor':
        return ['dashboard', 'team-management'].includes(menuItem);

      case 'manager':
        return ![
          'settings'
        ].includes(menuItem);

      case 'admin':
        return true; // Admin has access to everything

      default:
        return false;
    }
  };

  const canAccessDashboardCard = (cardType: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return false; // Finance role hides all dashboard cards
      
      case 'factory_supervisor':
        return ['factory-output'].includes(cardType);
      
      case 'yard_supervisor':
        return ['setting-teams', 'dehacking', 'fuel-management'].includes(cardType);
      
      case 'manager':
        return true; // Manager sees all dashboard cards
      
      case 'admin':
        return true; // Admin sees all dashboard cards
      
      default:
        return false;
    }
  };

  const contextValue: UserContextType = {
    currentUser,
    setCurrentUser,
    userRole,
    hasAccess,
    canAccessMenuItem,
    canAccessDashboardCard,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
