import { cn } from "@/lib/utils";
import { 
  LayoutDashboard, 
  Hammer, 
  Users, 
  Flame, 
  Truck, 
  Archive, 
  CreditCard, 
  FileText, 
  Fuel, 
  Settings,
  ChevronLeft,
  ChevronRight,
  Database
} from "lucide-react";
import { MenuItem } from "@/pages/Index";

interface SidebarProps {
  activeMenuItem: MenuItem;
  onMenuItemClick: (item: MenuItem) => void;
  collapsed: boolean;
  onToggleCollapse: () => void;
}

const menuItems = [
  { id: "dashboard" as MenuItem, label: "Dashboard", icon: LayoutDashboard },
  { id: "dehacking" as MenuItem, label: "Dehacking", icon: Hammer },
  { id: "employees" as MenuItem, label: "Employees", icon: Users },
  { id: "team-management" as MenuItem, label: "Team Management", icon: Users },
  { id: "kilns" as MenuItem, label: "Kilns", icon: Flame },
  { id: "pallet-tracking" as MenuItem, label: "Pallet Tracking", icon: Truck },
  { id: "brick-types" as MenuItem, label: "Brick Types", icon: Archive },
  { id: "payments" as MenuItem, label: "Payments", icon: CreditCard },
  { id: "reports" as MenuItem, label: "Reports", icon: FileText },
  { id: "fuel-management" as MenuItem, label: "Fuel Management", icon: Fuel },
  { id: "assets" as MenuItem, label: "Assets", icon: Database },
  { id: "settings" as MenuItem, label: "Settings", icon: Settings },
];

export const Sidebar = ({ activeMenuItem, onMenuItemClick, collapsed, onToggleCollapse }: SidebarProps) => {
  return (
    <div className={cn(
      "bg-slate-800 text-white transition-all duration-300 flex flex-col",
      collapsed ? "w-16" : "w-64"
    )}>
      <div className="p-4 border-b border-slate-700 flex items-center justify-between">
        {!collapsed && (
          <h1 className="text-lg font-semibold text-white">Worcester Bakstone</h1>
        )}
        <button
          onClick={onToggleCollapse}
          className="p-1 rounded hover:bg-slate-700 transition-colors"
        >
          {collapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
        </button>
      </div>
      
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeMenuItem === item.id;
            
            return (
              <li key={item.id}>
                <button
                  onClick={() => onMenuItemClick(item.id)}
                  className={cn(
                    "w-full flex items-center gap-3 px-3 py-2 rounded-lg transition-colors text-left",
                    isActive 
                      ? "bg-slate-700 text-white" 
                      : "text-slate-300 hover:text-white hover:bg-slate-700"
                  )}
                >
                  <Icon size={20} className="flex-shrink-0" />
                  {!collapsed && <span className="truncate">{item.label}</span>}
                </button>
              </li>
            );
          })}
        </ul>
      </nav>
    </div>
  );
};
