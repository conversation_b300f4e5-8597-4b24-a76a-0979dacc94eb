# Edge Function Troubleshooting Guide

## Deployment Steps

1. **Install Supabase CLI**

   ```bash
   npm install -g supabase
   ```

2. **Login to Supabase**

   ```bash
   supabase login
   ```

3. **Deploy the Edge Function**

   ```bash
   supabase functions deploy manage-users --project-ref hhxwnoreclckmtenugmt
   ```

4. **Set Environment Variables**

   ```bash
   supabase secrets set SUPABASE_URL=https://hhxwnoreclckmtenugmt.supabase.co --project-ref hhxwnoreclckmtenugmt
   supabase secrets set SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTkxMDY4OSwiZXhwIjoyMDY1NDg2Njg5fQ.qfKkAhXg4fTVqX2gULF-aQw6EuPazZNI0EGsfYldsk0 --project-ref hhxwnoreclckmtenugmt
   supabase secrets set SUPABASE_ANON_KEY=your_anon_key --project-ref hhxwnoreclckmtenugmt
   ```

   Replace `your_service_role_key` and `your_anon_key` with your actual keys from the Supabase dashboard.

## Testing the Edge Function

1. **Run the Test Script**

   ```bash
   node test-edge-function.js
   ```

2. **Check Logs in Supabase Dashboard**

   Go to your Supabase project dashboard, navigate to Edge Functions, select the `manage-users` function, and check the logs for any errors.

## Common Issues and Solutions

### 1. Function Not Found

**Error**: 404 Not Found when trying to access the Edge Function

**Solution**: Make sure the function is properly deployed. Run the deployment command again and check for any errors.

### 2. Missing Environment Variables

**Error**: "SUPABASE_URL environment variable is not set" or similar errors

**Solution**: Set the environment variables using the Supabase CLI as shown above.

### 3. CORS Issues

**Error**: CORS errors in the browser console

**Solution**: The Edge Function already has CORS headers set up. Make sure your frontend is making requests with the correct headers.

### 4. Authentication Issues

**Error**: "JWT expired" or "Invalid JWT"

**Solution**: Make sure you're using a valid Supabase API key. For testing, use the anon key. For production, consider using the service role key with proper security measures.

### 5. Database Access Issues

**Error**: "Error creating user: permission denied for table users"

**Solution**: 
- If using the anon key, make sure your RLS policies allow the necessary operations on the users table.
- If using the service role key, make sure it's correctly set in the environment variables.

## Database Schema

Make sure your Supabase database has a `users` table with the following schema:

```sql
CREATE TABLE public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    password_hash TEXT NOT NULL,
    role TEXT NOT NULL,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);
```

You may also need to create a user_role enum type:

```sql
CREATE TYPE public.user_role AS ENUM (
    'admin',
    'manager',
    'factory_supervisor',
    'yard_supervisor',
    'finance'
);
```

## RLS Policies

If you're using the anon key, you'll need to set up RLS policies to allow the necessary operations. Here's an example:

```sql
-- Enable RLS on the users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Allow authenticated users to read all users
CREATE POLICY "Allow authenticated users to read all users" 
    ON public.users FOR SELECT 
    TO authenticated 
    USING (true);

-- Allow authenticated users with admin role to insert users
CREATE POLICY "Allow admins to insert users" 
    ON public.users FOR INSERT 
    TO authenticated 
    WITH CHECK (auth.jwt() ->> 'role' = 'admin');

-- Allow authenticated users with admin role to update users
CREATE POLICY "Allow admins to update users" 
    ON public.users FOR UPDATE 
    TO authenticated 
    USING (auth.jwt() ->> 'role' = 'admin');

-- Allow authenticated users with admin role to delete users
CREATE POLICY "Allow admins to delete users" 
    ON public.users FOR DELETE 
    TO authenticated 
    USING (auth.jwt() ->> 'role' = 'admin');
```

## Additional Resources

- [Supabase Edge Functions Documentation](https://supabase.com/docs/guides/functions)
- [Supabase CLI Documentation](https://supabase.com/docs/reference/cli/introduction)
- [Supabase Authentication Documentation](https://supabase.com/docs/guides/auth)
- [Supabase Row Level Security Documentation](https://supabase.com/docs/guides/auth/row-level-security)