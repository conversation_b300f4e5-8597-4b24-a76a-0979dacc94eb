import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { useAuth } from "@/contexts/AuthContext";
import { Clock, Activity, Users, Fuel, Factory, Trash2 } from "lucide-react";

export const DailyActivitySummary = () => {
  const { currentUser, loginTime, getSessionDuration } = useAuth();
  const { getTodaysActivities, getActivitySummary, clearTodaysActivities } = useActivityTracking();
  
  const todaysActivities = getTodaysActivities();
  const summary = getActivitySummary();

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'production': return <Factory size={16} />;
      case 'team_management': return <Users size={16} />;
      case 'fuel': return <Fuel size={16} />;
      default: return <Activity size={16} />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'production': return 'bg-blue-100 text-blue-800';
      case 'team_management': return 'bg-green-100 text-green-800';
      case 'fuel': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const handleClearActivities = () => {
    if (window.confirm('Are you sure you want to clear today\'s activity log? This cannot be undone.')) {
      clearTodaysActivities();
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Clock size={20} />
              Daily Activity Summary
            </CardTitle>
            <CardDescription>
              Your activities for {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </CardDescription>
          </div>
          {todaysActivities.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearActivities}
              className="text-red-600 hover:text-red-700"
            >
              <Trash2 size={16} className="mr-2" />
              Clear Log
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Session Info */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm text-blue-600 font-medium">Session Time</div>
            <div className="text-lg font-bold text-blue-800">{summary.sessionDuration}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-sm text-green-600 font-medium">Total Activities</div>
            <div className="text-lg font-bold text-green-800">{summary.totalActivities}</div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="text-sm text-orange-600 font-medium">Production Entries</div>
            <div className="text-lg font-bold text-orange-800">{summary.productionEntries}</div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="text-sm text-purple-600 font-medium">Team Actions</div>
            <div className="text-lg font-bold text-purple-800">{summary.teamManagementActions}</div>
          </div>
        </div>

        {/* Login Time */}
        {loginTime && (
          <div className="bg-slate-50 p-3 rounded-lg">
            <div className="text-sm text-slate-600">
              Logged in at: <span className="font-medium">{formatTime(loginTime)}</span>
            </div>
          </div>
        )}

        {/* Recent Activities */}
        <div>
          <h4 className="font-medium text-slate-800 mb-3">Recent Activities</h4>
          {todaysActivities.length === 0 ? (
            <div className="text-center py-6 text-slate-500">
              <Activity size={32} className="mx-auto mb-2 opacity-50" />
              <p>No activities recorded today</p>
              <p className="text-sm">Start working to see your activities here</p>
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {todaysActivities.slice(0, 10).map((activity) => (
                <div key={activity.id} className="flex items-center gap-3 p-3 bg-white border rounded-lg">
                  <div className={`p-2 rounded-full ${getCategoryColor(activity.category)}`}>
                    {getCategoryIcon(activity.category)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-slate-800 truncate">{activity.action}</div>
                    <div className="text-sm text-slate-600 truncate">{activity.details}</div>
                  </div>
                  <div className="text-xs text-slate-500">
                    {formatTime(activity.timestamp)}
                  </div>
                </div>
              ))}
              {todaysActivities.length > 10 && (
                <div className="text-center text-sm text-slate-500 py-2">
                  ... and {todaysActivities.length - 10} more activities
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
