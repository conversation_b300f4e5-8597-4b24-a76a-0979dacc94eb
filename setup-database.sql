-- This script sets up the necessary database schema for the user management system

-- Create user_role enum type if it doesn't exist
CREATE TYPE IF NOT EXISTS public.user_role AS ENUM (
    'admin',
    'manager',
    'factory_supervisor',
    'yard_supervisor',
    'finance'
);

-- Create users table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    email TEXT,
    password_hash TEXT NOT NULL,
    role user_role NOT NULL,
    active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create RLS policies

-- Allow authenticated users to read all users
DROP POLICY IF EXISTS "Allow authenticated users to read all users" ON public.users;
CREATE POLICY "Allow authenticated users to read all users" 
    ON public.users FOR SELECT 
    TO authenticated 
    USING (true);

-- Allow authenticated users to insert users
DROP POLICY IF EXISTS "Allow authenticated users to insert users" ON public.users;
CREATE POLICY "Allow authenticated users to insert users" 
    ON public.users FOR INSERT 
    TO authenticated 
    WITH CHECK (true);

-- Allow authenticated users to update users
DROP POLICY IF EXISTS "Allow authenticated users to update users" ON public.users;
CREATE POLICY "Allow authenticated users to update users" 
    ON public.users FOR UPDATE 
    TO authenticated 
    USING (true);

-- Allow authenticated users to delete users
DROP POLICY IF EXISTS "Allow authenticated users to delete users" ON public.users;
CREATE POLICY "Allow authenticated users to delete users" 
    ON public.users FOR DELETE 
    TO authenticated 
    USING (true);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON public.users
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();