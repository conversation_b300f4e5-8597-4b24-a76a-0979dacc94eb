
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

export type UserRole = Database["public"]["Enums"]["user_role"];

export interface User {
  id: string;
  username: string;
  full_name: string;
  email?: string;
  role: UserRole;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  username: string;
  full_name: string;
  email?: string;
  password: string;
  role: UserRole;
  active: boolean;
}

export interface UpdateUserData {
  username?: string;
  full_name?: string;
  email?: string;
  role?: UserRole;
  active?: boolean;
}

export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .order("created_at", { ascending: false });
      
      if (error) throw new Error(error.message);
      return data as User[];
    }
  });
}

export function useCreateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (userData: CreateUserData) => {
      try {
        // Hash the password using a simple client-side hash
        // Note: This is not as secure as server-side hashing but works for demo purposes
        const encoder = new TextEncoder();
        const data = encoder.encode(userData.password);
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const passwordHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        
        // Insert user directly into the database
        const { data: user, error } = await supabase
          .from("users")
          .insert({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            password_hash: passwordHash,
            role: userData.role,
            active: userData.active,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .select()
          .single();
        
        if (error) {
          console.error('User creation error:', error);
          throw new Error(`Failed to create user: ${error.message}`);
        }
        
        return user;
      } catch (err) {
        console.error('User creation exception:', err);
        throw new Error(err instanceof Error ? err.message : 'Failed to create user. Please check your database configuration.');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUserData }) => {
      const { data: result, error } = await supabase.functions.invoke('manage-users', {
        body: {
          action: 'update',
          userId: id,
          userData: data
        }
      });
      
      if (error) throw new Error(error.message);
      if (!result.success) throw new Error(result.error || 'Failed to update user');
      
      return result.user;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const { data, error } = await supabase.functions.invoke('manage-users', {
        body: {
          action: 'delete',
          userId: id
        }
      });
      
      if (error) throw new Error(error.message);
      if (!data.success) throw new Error(data.error || 'Failed to delete user');
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async ({ userId, currentPassword, newPassword }: { 
      userId: string; 
      currentPassword: string; 
      newPassword: string; 
    }) => {
      const { data, error } = await supabase.functions.invoke('manage-users', {
        body: {
          action: 'change-password',
          userId,
          passwords: { currentPassword, newPassword }
        }
      });
      
      if (error) throw new Error(error.message);
      if (!data.success) throw new Error(data.error || 'Failed to change password');
    }
  });
}
